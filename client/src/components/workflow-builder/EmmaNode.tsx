/**
 * Componente de nodo personalizado para Emma Workflows
 * Nodo visual con inputs/outputs y configuración
 */

import React, { memo, useState, useRef, useCallback } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { motion } from 'framer-motion';
import { Settings, X, Check, AlertTriangle, Upload, Image as ImageIcon, Trash2 } from 'lucide-react';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { EmmaNodeData, InputConfig } from '@/types/workflow-types';
import { EMMA_NODES } from '@/data/emma-nodes';

interface EmmaNodeProps extends NodeProps {
  data: EmmaNodeData;
}

const EmmaNode: React.FC<EmmaNodeProps> = ({ id, data, selected }) => {
  const [showConfig, setShowConfig] = useState(false);
  const [localInputs, setLocalInputs] = useState(data.inputs || {});
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const nodeDefinition = EMMA_NODES[data.nodeType];
  if (!nodeDefinition) {
    return (
      <Card className="min-w-[200px] border-red-500 bg-red-500/10">
        <CardContent className="p-4">
          <div className="text-red-300 text-sm">Nodo desconocido: {data.nodeType}</div>
        </CardContent>
      </Card>
    );
  }

  const handleInputChange = (inputName: string, value: any) => {
    setLocalInputs(prev => ({
      ...prev,
      [inputName]: value
    }));

    // Actualizar inmediatamente los datos del nodo
    data.inputs = {
      ...data.inputs,
      [inputName]: value
    };
  };

  const saveConfiguration = () => {
    // Guardar todos los inputs locales en el nodo
    data.inputs = { ...localInputs };
    setShowConfig(false);
  };

  // Manejo de subida de imágenes
  const handleImageUpload = useCallback(async (file: File) => {
    setIsUploading(true);
    try {
      // Convertir a base64 para preview inmediato
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result as string;
        setUploadedImage(base64);
        handleInputChange('image', base64);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error subiendo imagen:', error);
    } finally {
      setIsUploading(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));
    if (imageFile) {
      handleImageUpload(imageFile);
    }
  }, [handleImageUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      handleImageUpload(file);
    }
  }, [handleImageUpload]);

  const removeImage = useCallback(() => {
    setUploadedImage(null);
    handleInputChange('image', '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const renderInputField = (inputName: string, config: InputConfig) => {
    const value = localInputs[inputName] || config.default || '';

    switch (config.type) {
      case 'text':
        if (config.options) {
          return (
            <Select value={value} onValueChange={(val) => handleInputChange(inputName, val)}>
              <SelectTrigger className="bg-black/20 border-purple-500/30">
                <SelectValue placeholder={config.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {config.options.map(option => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }
        
        if (config.placeholder?.includes('...') || inputName.includes('prompt')) {
          return (
            <Textarea
              value={value}
              onChange={(e) => handleInputChange(inputName, e.target.value)}
              placeholder={config.placeholder}
              className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
              rows={3}
            />
          );
        }
        
        return (
          <Input
            value={value}
            onChange={(e) => handleInputChange(inputName, e.target.value)}
            placeholder={config.placeholder}
            className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleInputChange(inputName, parseFloat(e.target.value) || 0)}
            min={config.min}
            max={config.max}
            step={config.step}
            className="bg-black/20 border-purple-500/30 text-white"
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleInputChange(inputName, e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-black/20 border-purple-500/30 rounded focus:ring-purple-500"
            />
            <span className="text-sm text-purple-200">
              {value ? 'Activado' : 'Desactivado'}
            </span>
          </div>
        );

      case 'image':
        return (
          <div className="space-y-3">
            {/* Área de drop para imágenes */}
            <div
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
              onDragEnter={(e) => e.preventDefault()}
              className="border-2 border-dashed border-purple-500/30 rounded-lg p-4 text-center hover:border-purple-400/50 transition-colors cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              {isUploading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm text-purple-200">Subiendo...</span>
                </div>
              ) : uploadedImage || value ? (
                <div className="space-y-2">
                  <img
                    src={uploadedImage || value}
                    alt="Preview"
                    className="max-w-full max-h-32 mx-auto rounded-lg object-cover"
                  />
                  <div className="flex items-center justify-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeImage();
                      }}
                      className="text-red-400 hover:bg-red-500/20"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Eliminar
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="w-8 h-8 mx-auto text-purple-400" />
                  <div className="text-sm text-purple-200">
                    Arrastra una imagen aquí o haz clic para seleccionar
                  </div>
                  <div className="text-xs text-purple-300">
                    PNG, JPG, JPEG hasta 10MB
                  </div>
                </div>
              )}
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        );

      default:
        return (
          <div className="text-xs text-purple-300 p-2 bg-purple-500/10 rounded">
            Conexión requerida: {config.type}
          </div>
        );
    }
  };

  // Estilo especial para nodo de prompt (como Weavy)
  const isPromptNode = data.nodeType === 'text-input';
  const isAINode = ['ideogram-generator', 'dalle-generator', 'video-generator'].includes(data.nodeType);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        className={`relative ${selected ? 'ring-2 ring-purple-400' : ''}`}
      >
        <Card
          className={`
            ${isPromptNode ? 'min-w-[400px] max-w-[500px]' : 'min-w-[250px] max-w-[300px]'}
            border transition-all duration-200
            ${selected
              ? 'border-purple-400 shadow-lg shadow-purple-500/20'
              : 'border-purple-500/30 hover:border-purple-400/50'
            }
            ${data.isExecuting
              ? 'bg-yellow-500/10 border-yellow-400'
              : data.isCompleted
                ? 'bg-green-500/10 border-green-400'
                : 'bg-black/40'
            }
            ${isPromptNode ? 'bg-gradient-to-br from-purple-900/50 to-pink-900/50 border-purple-400/50' : ''}
          `}
          style={{ backgroundColor: `${nodeDefinition.color}15` }}
        >
          {/* Handles de entrada */}
          {Object.entries(nodeDefinition.inputs).map(([inputName, inputConfig], index) => (
            <Handle
              key={`input-${inputName}`}
              type="target"
              position={Position.Left}
              id={inputName}
              style={{
                top: 60 + index * 25,
                background: '#8b5cf6',
                border: '2px solid #1f2937',
                width: 14,
                height: 14,
                borderRadius: '50%'
              }}
              isConnectable={true}
            >
              <div
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-purple-300 whitespace-nowrap pointer-events-none"
                style={{ fontSize: '10px' }}
              >
                {inputConfig.label}
              </div>
            </Handle>
          ))}

          {/* Handles de salida */}
          {Object.entries(nodeDefinition.outputs).map(([outputName, outputConfig], index) => (
            <Handle
              key={`output-${outputName}`}
              type="source"
              position={Position.Right}
              id={outputName}
              style={{
                top: 60 + index * 25,
                background: '#10b981',
                border: '2px solid #1f2937',
                width: 14,
                height: 14,
                borderRadius: '50%'
              }}
              isConnectable={true}
            >
              <div
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-green-300 whitespace-nowrap pointer-events-none"
                style={{ fontSize: '10px' }}
              >
                {outputConfig.label}
              </div>
            </Handle>
          ))}

          <CardHeader className={`${isPromptNode ? 'p-4 pb-3' : 'p-3 pb-2'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className={`${isPromptNode ? 'text-2xl' : 'text-lg'}`}>
                  {nodeDefinition.icon}
                </span>
                <div>
                  <h3 className={`${isPromptNode ? 'text-lg' : 'text-sm'} font-medium text-white`}>
                    {nodeDefinition.name}
                  </h3>
                  <p className={`${isPromptNode ? 'text-sm' : 'text-xs'} text-purple-200`}>
                    {nodeDefinition.category}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {data.isExecuting && (
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
                )}
                {data.isCompleted && (
                  <Check className="w-4 h-4 text-green-400" />
                )}
                {data.errors.length > 0 && (
                  <AlertTriangle className="w-4 h-4 text-red-400" />
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowConfig(!showConfig)}
                  className="w-6 h-6 p-0 text-purple-300 hover:bg-purple-500/20"
                >
                  <Settings className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className={`${isPromptNode ? 'p-4 pt-0' : 'p-3 pt-0'}`}>
            <p className={`${isPromptNode ? 'text-sm' : 'text-xs'} text-purple-200 mb-3`}>
              {nodeDefinition.description}
            </p>

            {/* Mostrar datos conectados */}
            {Object.keys(data.inputs || {}).length > 0 && (
              <div className="mb-3 space-y-1">
                <p className="text-xs text-purple-200 font-medium">📥 Datos conectados:</p>
                {Object.entries(data.inputs).map(([key, value]) => {
                  const inputConfig = nodeDefinition.inputs[key];
                  if (!inputConfig || !value) return null;

                  return (
                    <div key={key} className="text-xs bg-green-500/10 border border-green-500/20 rounded px-2 py-1">
                      <span className="text-green-300 font-medium">{inputConfig.label}:</span>
                      <span className="text-green-200 ml-1">
                        {typeof value === 'string'
                          ? value.length > 25 ? value.substring(0, 25) + '...' : value
                          : '✓ Conectado'
                        }
                      </span>
                    </div>
                  );
                })}
              </div>
            )}

            {/* PROMPT GRANDE como Weavy */}
            {isPromptNode && data.inputs?.prompt && (
              <div className="mb-4 p-3 bg-black/30 rounded-lg border border-purple-500/30">
                <div className="text-sm text-white font-medium mb-2">💭 Prompt Principal</div>
                <div className="text-sm text-purple-100 leading-relaxed max-h-24 overflow-y-auto">
                  {data.inputs.prompt}
                </div>
              </div>
            )}

            {/* Preview especial para nodos de imagen */}
            {data.nodeType === 'image-input' && (uploadedImage || data.inputs?.image) && (
              <div className="mb-3">
                <img
                  src={uploadedImage || data.inputs?.image}
                  alt="Preview"
                  className="w-full max-h-24 object-cover rounded-lg border border-purple-500/30"
                />
              </div>
            )}

            {/* Preview GRANDE para nodos de AI como Weavy */}
            {(data.nodeType === 'ideogram-generator' || data.nodeType === 'dalle-generator') && data.outputs?.image && (
              <div className="mb-4">
                <div className="text-sm text-white font-medium mb-2 flex items-center space-x-2">
                  <span>🎨</span>
                  <span>Imagen Generada</span>
                </div>
                <img
                  src={data.outputs.image}
                  alt="Generated"
                  className="w-full h-32 object-cover rounded-lg border border-green-500/30 shadow-lg"
                />
                <div className="text-xs text-green-300 mt-2 flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>Generación completada</span>
                </div>
              </div>
            )}

            {/* Preview para video */}
            {data.nodeType === 'video-generator' && data.outputs?.video && (
              <div className="mb-4">
                <div className="text-sm text-white font-medium mb-2 flex items-center space-x-2">
                  <span>🎬</span>
                  <span>Video Generado</span>
                </div>
                <video
                  src={data.outputs.video}
                  className="w-full h-32 object-cover rounded-lg border border-green-500/30 shadow-lg"
                  controls
                  muted
                />
                <div className="text-xs text-green-300 mt-2 flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>Video listo</span>
                </div>
              </div>
            )}

            {/* Inputs básicos */}
            {Object.entries(nodeDefinition.inputs).map(([inputName, config]) => (
              <div key={inputName} className="mb-2">
                <div className="flex items-center space-x-2 text-xs">
                  <div className="w-2 h-2 bg-purple-400 rounded-full" />
                  <span className="text-purple-200">{config.label}</span>
                  {config.required && <span className="text-red-400">*</span>}
                  {data.inputs?.[inputName] && (
                    <Check className="w-3 h-3 text-green-400" />
                  )}
                </div>
              </div>
            ))}

            {/* Outputs */}
            <div className="mt-3 pt-2 border-t border-purple-500/20">
              {Object.entries(nodeDefinition.outputs).map(([outputName, config]) => (
                <div key={outputName} className="flex items-center justify-end space-x-2 text-xs mb-1">
                  <span className="text-purple-200">{config.label}</span>
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Indicador de ejecución */}
        {data.isExecuting && (
          <div className="absolute -top-2 -right-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full animate-ping" />
          </div>
        )}
      </motion.div>

      {/* Panel de configuración */}
      {showConfig && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
          onClick={() => setShowConfig(false)}
        >
          <Card 
            className="bg-black/80 backdrop-blur-sm border-purple-500/30 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-white">
                  {nodeDefinition.icon} {nodeDefinition.name}
                </h3>
                <p className="text-sm text-purple-200">{nodeDefinition.description}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowConfig(false)}
                className="text-purple-300 hover:bg-purple-500/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </CardHeader>

            <CardContent className="space-y-4">
              {Object.entries(nodeDefinition.inputs).map(([inputName, config]) => (
                <div key={inputName} className="space-y-2">
                  <Label className="text-sm font-medium text-purple-200 flex items-center space-x-2">
                    <span>{config.label}</span>
                    {config.required && <span className="text-red-400">*</span>}
                  </Label>
                  {renderInputField(inputName, config)}
                  {config.tooltip && (
                    <p className="text-xs text-purple-300">{config.tooltip}</p>
                  )}
                </div>
              ))}

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowConfig(false)}
                  className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={saveConfiguration}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  Guardar
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </>
  );
};

export default memo(EmmaNode);
