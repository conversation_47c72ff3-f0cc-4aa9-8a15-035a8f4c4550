/**
 * Definiciones de nodos Emma para el Workflow Builder
 * Sistema simplificado y fácil de usar
 */

import { EmmaNodeDefinition } from '@/types/workflow-types';

// 📝 NODOS DE ENTRADA
export const textInputNode: EmmaNodeDefinition = {
  id: 'text-input',
  name: 'Entrada de Texto',
  category: 'Entrada',
  description: 'Ingresa texto o prompts para generar contenido',
  icon: '📝',
  color: '#10b981',
  inputs: {},
  outputs: {
    text: { type: 'text', label: 'Texto' }
  }
};

export const imageInputNode: EmmaNodeDefinition = {
  id: 'image-input',
  name: 'Subir Imagen',
  category: 'Entrada',
  description: 'Sube una imagen para procesar',
  icon: '🖼️',
  color: '#10b981',
  inputs: {},
  outputs: {
    image: { type: 'image', label: 'Imagen' }
  }
};

// 🎨 NODOS DE GENERACIÓN DE IMÁGENES
export const ideogramGeneratorNode: EmmaNodeDefinition = {
  id: 'ideogram-generator',
  name: 'Ideogram AI',
  category: 'Generación',
  description: 'Genera imágenes con Ideogram AI',
  icon: '🎨',
  color: '#3b82f6',
  provider: 'ideogram',
  endpoint: '/api/image-generator/generate',
  inputs: {
    prompt: { 
      type: 'text', 
      label: 'Prompt', 
      required: true,
      placeholder: 'Describe la imagen que quieres generar...'
    },
    model: { 
      type: 'text', 
      label: 'Modelo', 
      default: 'ideogram-3.0',
      options: ['ideogram-3.0', 'ideogram-2.0']
    },
    aspect_ratio: { 
      type: 'text', 
      label: 'Aspecto', 
      default: '1:1',
      options: ['1:1', '16:9', '9:16', '4:3', '3:4']
    },
    style: { 
      type: 'text', 
      label: 'Estilo', 
      default: 'auto',
      options: ['auto', 'realistic', 'design', 'anime', '3d']
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen' },
    url: { type: 'url', label: 'URL' }
  }
};

export const dalleGeneratorNode: EmmaNodeDefinition = {
  id: 'dalle-generator',
  name: 'DALL-E 3',
  category: 'Generación',
  description: 'Genera imágenes con OpenAI DALL-E',
  icon: '🤖',
  color: '#8b5cf6',
  provider: 'openai',
  endpoint: '/api/v1/openai-images/generate',
  inputs: {
    prompt: { 
      type: 'text', 
      label: 'Prompt', 
      required: true,
      placeholder: 'Describe la imagen...'
    },
    model: { 
      type: 'text', 
      label: 'Modelo', 
      default: 'dall-e-3',
      options: ['dall-e-3', 'dall-e-2']
    },
    size: { 
      type: 'text', 
      label: 'Tamaño', 
      default: '1024x1024',
      options: ['1024x1024', '1792x1024', '1024x1792']
    },
    quality: { 
      type: 'text', 
      label: 'Calidad', 
      default: 'standard',
      options: ['standard', 'hd']
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen' },
    url: { type: 'url', label: 'URL' }
  }
};

// ✨ NODOS DE EDICIÓN
export const upscaleNode: EmmaNodeDefinition = {
  id: 'upscale',
  name: 'Mejorar Imagen',
  category: 'Edición',
  description: 'Mejora la resolución y calidad de la imagen',
  icon: '⬆️',
  color: '#f59e0b',
  provider: 'stability',
  endpoint: '/api/v1/ai-editor/upscale',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true },
    scale_factor: { 
      type: 'number', 
      label: 'Factor', 
      default: 2,
      options: ['2', '4']
    },
    creativity: { 
      type: 'number', 
      label: 'Creatividad', 
      default: 0.3,
      min: 0,
      max: 1,
      step: 0.1
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen Mejorada' },
    url: { type: 'url', label: 'URL' }
  }
};

export const backgroundRemoverNode: EmmaNodeDefinition = {
  id: 'background-remover',
  name: 'Remover Fondo',
  category: 'Edición',
  description: 'Remueve el fondo de la imagen automáticamente',
  icon: '🗑️',
  color: '#ef4444',
  provider: 'stability',
  endpoint: '/api/stability-remove-bg/remove-background',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true }
  },
  outputs: {
    image: { type: 'image', label: 'Sin Fondo' },
    url: { type: 'url', label: 'URL' }
  }
};

export const styleTransferNode: EmmaNodeDefinition = {
  id: 'style-transfer',
  name: 'Transferir Estilo',
  category: 'Edición',
  description: 'Aplica el estilo de una imagen a otra',
  icon: '🎭',
  color: '#ec4899',
  provider: 'emma',
  endpoint: '/api/v1/images/style-transfer',
  inputs: {
    content_image: { type: 'image', label: 'Imagen Base', required: true },
    style_image: { type: 'image', label: 'Imagen de Estilo', required: true },
    strength: { 
      type: 'number', 
      label: 'Intensidad', 
      default: 0.7,
      min: 0.1,
      max: 1.0,
      step: 0.1
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen Estilizada' },
    url: { type: 'url', label: 'URL' }
  }
};

// 🎬 NODOS DE VIDEO
export const videoGeneratorNode: EmmaNodeDefinition = {
  id: 'video-generator',
  name: 'Luma Labs Video',
  category: 'Video',
  description: 'Genera videos con Luma Labs AI',
  icon: '🎬',
  color: '#06b6d4',
  provider: 'luma',
  endpoint: '/api/luma-labs/generate',
  inputs: {
    prompt: {
      type: 'text',
      label: 'Prompt de Video',
      required: true,
      placeholder: 'Describe el movimiento del video...'
    },
    reference_image: { type: 'image', label: 'Imagen de Referencia' },
    duration: {
      type: 'text',
      label: 'Duración',
      default: '5',
      options: ['5', '10', '15']
    },
    aspect_ratio: {
      type: 'text',
      label: 'Aspecto',
      default: '16:9',
      options: ['16:9', '9:16', '1:1']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video' },
    url: { type: 'url', label: 'URL' }
  }
};

export const pikaVideoNode: EmmaNodeDefinition = {
  id: 'pika-video-generator',
  name: 'Pika Labs Video',
  category: 'Video',
  description: 'Genera videos con Pika Labs AI',
  icon: '⚡',
  color: '#f59e0b',
  provider: 'pika',
  endpoint: '/api/pika-labs/generate',
  inputs: {
    prompt: {
      type: 'text',
      label: 'Prompt de Video',
      required: true,
      placeholder: 'Describe la escena del video...'
    },
    reference_image: { type: 'image', label: 'Imagen de Referencia' },
    style: {
      type: 'text',
      label: 'Estilo',
      default: 'realistic',
      options: ['realistic', 'anime', 'cartoon', '3d', 'cinematic']
    },
    motion_strength: {
      type: 'number',
      label: 'Intensidad de Movimiento',
      default: 0.7,
      min: 0.1,
      max: 1.0,
      step: 0.1
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video' },
    url: { type: 'url', label: 'URL' }
  }
};

export const runwayVideoNode: EmmaNodeDefinition = {
  id: 'runway-video-generator',
  name: 'Runway ML Video',
  category: 'Video',
  description: 'Genera videos con Runway ML Gen-3',
  icon: '🛫',
  color: '#8b5cf6',
  provider: 'runway',
  endpoint: '/api/runway-ml/generate',
  inputs: {
    prompt: {
      type: 'text',
      label: 'Prompt de Video',
      required: true,
      placeholder: 'Describe el video que quieres crear...'
    },
    reference_image: { type: 'image', label: 'Imagen de Referencia' },
    duration: {
      type: 'text',
      label: 'Duración',
      default: '10',
      options: ['5', '10', '15', '20']
    },
    camera_motion: {
      type: 'text',
      label: 'Movimiento de Cámara',
      default: 'static',
      options: ['static', 'pan_left', 'pan_right', 'zoom_in', 'zoom_out', 'orbit']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video' },
    url: { type: 'url', label: 'URL' }
  }
};

export const stableVideoNode: EmmaNodeDefinition = {
  id: 'stable-video-diffusion',
  name: 'Stable Video Diffusion',
  category: 'Video',
  description: 'Convierte imágenes a video con Stable Video Diffusion',
  icon: '🌊',
  color: '#10b981',
  provider: 'stability',
  endpoint: '/api/stability-video/generate',
  inputs: {
    image: { type: 'image', label: 'Imagen Base', required: true },
    motion_bucket_id: {
      type: 'number',
      label: 'Intensidad de Movimiento',
      default: 127,
      min: 1,
      max: 255
    },
    cond_aug: {
      type: 'number',
      label: 'Augmentación Condicional',
      default: 0.02,
      min: 0.0,
      max: 1.0,
      step: 0.01
    },
    seed: {
      type: 'number',
      label: 'Semilla',
      default: 0,
      min: 0,
      max: **********
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video' },
    url: { type: 'url', label: 'URL' }
  }
};

export const animateDiffVideoNode: EmmaNodeDefinition = {
  id: 'animate-diff-video',
  name: 'AnimateDiff Video',
  category: 'Video',
  description: 'Genera videos animados con AnimateDiff',
  icon: '🎭',
  color: '#ec4899',
  provider: 'animatediff',
  endpoint: '/api/animate-diff/generate',
  inputs: {
    prompt: {
      type: 'text',
      label: 'Prompt de Animación',
      required: true,
      placeholder: 'Describe la animación...'
    },
    negative_prompt: {
      type: 'text',
      label: 'Prompt Negativo',
      placeholder: 'Lo que NO quieres en el video...'
    },
    frames: {
      type: 'number',
      label: 'Número de Frames',
      default: 16,
      min: 8,
      max: 32
    },
    guidance_scale: {
      type: 'number',
      label: 'Escala de Guía',
      default: 7.5,
      min: 1.0,
      max: 20.0,
      step: 0.5
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video Animado' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoUpscaleNode: EmmaNodeDefinition = {
  id: 'video-upscale',
  name: 'Mejorar Video',
  category: 'Video',
  description: 'Mejora la calidad y resolución de videos',
  icon: '📈',
  color: '#f59e0b',
  provider: 'topaz',
  endpoint: '/api/video-enhance/upscale',
  inputs: {
    video: { type: 'video', label: 'Video Original', required: true },
    scale_factor: {
      type: 'text',
      label: 'Factor de Escala',
      default: '2x',
      options: ['2x', '4x', '8x']
    },
    model: {
      type: 'text',
      label: 'Modelo de Mejora',
      default: 'artemis-hq',
      options: ['artemis-hq', 'proteus-fine-tune', 'gaia-hq']
    },
    fps_conversion: {
      type: 'text',
      label: 'Conversión FPS',
      default: 'keep_original',
      options: ['keep_original', '24fps', '30fps', '60fps']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video Mejorado' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoTrimNode: EmmaNodeDefinition = {
  id: 'video-trim',
  name: 'Recortar Video',
  category: 'Video',
  description: 'Recorta y edita segmentos de video',
  icon: '✂️',
  color: '#ef4444',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/trim',
  inputs: {
    video: { type: 'video', label: 'Video Original', required: true },
    start_time: {
      type: 'number',
      label: 'Tiempo Inicio (seg)',
      default: 0,
      min: 0
    },
    end_time: {
      type: 'number',
      label: 'Tiempo Final (seg)',
      default: 10,
      min: 1
    },
    fade_in: {
      type: 'number',
      label: 'Fade In (seg)',
      default: 0,
      min: 0,
      max: 5,
      step: 0.1
    },
    fade_out: {
      type: 'number',
      label: 'Fade Out (seg)',
      default: 0,
      min: 0,
      max: 5,
      step: 0.1
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video Recortado' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoMergeNode: EmmaNodeDefinition = {
  id: 'video-merge',
  name: 'Unir Videos',
  category: 'Video',
  description: 'Une múltiples videos en uno solo',
  icon: '🔗',
  color: '#8b5cf6',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/merge',
  inputs: {
    video1: { type: 'video', label: 'Video 1', required: true },
    video2: { type: 'video', label: 'Video 2', required: true },
    video3: { type: 'video', label: 'Video 3 (Opcional)' },
    transition: {
      type: 'text',
      label: 'Transición',
      default: 'cut',
      options: ['cut', 'fade', 'slide', 'wipe']
    },
    transition_duration: {
      type: 'number',
      label: 'Duración Transición (seg)',
      default: 0.5,
      min: 0.1,
      max: 3.0,
      step: 0.1
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video Unido' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoTextOverlayNode: EmmaNodeDefinition = {
  id: 'video-text-overlay',
  name: 'Texto en Video',
  category: 'Video',
  description: 'Agrega texto y subtítulos a videos',
  icon: '📝',
  color: '#10b981',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/text-overlay',
  inputs: {
    video: { type: 'video', label: 'Video Base', required: true },
    text: {
      type: 'text',
      label: 'Texto',
      required: true,
      placeholder: 'Escribe el texto a mostrar...'
    },
    font_size: {
      type: 'number',
      label: 'Tamaño de Fuente',
      default: 48,
      min: 12,
      max: 200
    },
    position: {
      type: 'text',
      label: 'Posición',
      default: 'bottom',
      options: ['top', 'center', 'bottom', 'top-left', 'top-right', 'bottom-left', 'bottom-right']
    },
    color: {
      type: 'text',
      label: 'Color',
      default: 'white',
      options: ['white', 'black', 'red', 'blue', 'green', 'yellow', 'purple']
    },
    duration: {
      type: 'number',
      label: 'Duración (seg)',
      default: 5,
      min: 1,
      max: 60
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video con Texto' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoAudioNode: EmmaNodeDefinition = {
  id: 'video-audio-overlay',
  name: 'Audio en Video',
  category: 'Video',
  description: 'Agrega música y efectos de sonido a videos',
  icon: '🎵',
  color: '#f59e0b',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/audio-overlay',
  inputs: {
    video: { type: 'video', label: 'Video Base', required: true },
    audio_type: {
      type: 'text',
      label: 'Tipo de Audio',
      default: 'background_music',
      options: ['background_music', 'voice_over', 'sound_effects', 'custom_audio']
    },
    audio_file: { type: 'audio', label: 'Archivo de Audio' },
    volume: {
      type: 'number',
      label: 'Volumen (%)',
      default: 50,
      min: 0,
      max: 100
    },
    fade_in_audio: {
      type: 'number',
      label: 'Fade In Audio (seg)',
      default: 1,
      min: 0,
      max: 10,
      step: 0.1
    },
    loop_audio: {
      type: 'text',
      label: 'Repetir Audio',
      default: 'yes',
      options: ['yes', 'no']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video con Audio' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoEffectsNode: EmmaNodeDefinition = {
  id: 'video-effects',
  name: 'Efectos de Video',
  category: 'Video',
  description: 'Aplica efectos visuales y filtros a videos',
  icon: '✨',
  color: '#ec4899',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/effects',
  inputs: {
    video: { type: 'video', label: 'Video Base', required: true },
    effect_type: {
      type: 'text',
      label: 'Tipo de Efecto',
      default: 'blur',
      options: ['blur', 'sharpen', 'vintage', 'black_white', 'sepia', 'neon', 'glitch', 'zoom_blur']
    },
    intensity: {
      type: 'number',
      label: 'Intensidad',
      default: 0.5,
      min: 0.1,
      max: 2.0,
      step: 0.1
    },
    color_temperature: {
      type: 'number',
      label: 'Temperatura de Color',
      default: 0,
      min: -100,
      max: 100
    },
    saturation: {
      type: 'number',
      label: 'Saturación',
      default: 1.0,
      min: 0.0,
      max: 3.0,
      step: 0.1
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video con Efectos' },
    url: { type: 'url', label: 'URL' }
  }
};

export const videoSpeedNode: EmmaNodeDefinition = {
  id: 'video-speed-control',
  name: 'Control de Velocidad',
  category: 'Video',
  description: 'Cambia la velocidad y crea efectos de tiempo',
  icon: '⏱️',
  color: '#06b6d4',
  provider: 'ffmpeg',
  endpoint: '/api/video-edit/speed-control',
  inputs: {
    video: { type: 'video', label: 'Video Base', required: true },
    speed_factor: {
      type: 'number',
      label: 'Factor de Velocidad',
      default: 1.0,
      min: 0.1,
      max: 10.0,
      step: 0.1
    },
    effect_type: {
      type: 'text',
      label: 'Tipo de Efecto',
      default: 'constant',
      options: ['constant', 'ramp_up', 'ramp_down', 'time_lapse', 'slow_motion']
    },
    preserve_audio: {
      type: 'text',
      label: 'Preservar Audio',
      default: 'yes',
      options: ['yes', 'no', 'pitch_correct']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video Modificado' },
    url: { type: 'url', label: 'URL' }
  }
};

// 💾 NODOS DE SALIDA
export const imageOutputNode: EmmaNodeDefinition = {
  id: 'image-output',
  name: 'Guardar Imagen',
  category: 'Salida',
  description: 'Guarda la imagen final',
  icon: '💾',
  color: '#84cc16',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true },
    filename: { 
      type: 'text', 
      label: 'Nombre', 
      default: 'emma-workflow',
      placeholder: 'nombre-archivo'
    },
    format: { 
      type: 'text', 
      label: 'Formato', 
      default: 'PNG',
      options: ['PNG', 'JPEG', 'WEBP']
    }
  },
  outputs: {
    url: { type: 'url', label: 'URL Final' }
  }
};

export const videoOutputNode: EmmaNodeDefinition = {
  id: 'video-output',
  name: 'Guardar Video',
  category: 'Salida',
  description: 'Guarda el video final',
  icon: '🎥',
  color: '#84cc16',
  inputs: {
    video: { type: 'video', label: 'Video', required: true },
    filename: { 
      type: 'text', 
      label: 'Nombre', 
      default: 'emma-video',
      placeholder: 'nombre-video'
    }
  },
  outputs: {
    url: { type: 'url', label: 'URL Final' }
  }
};

// Exportar todos los nodos
export const EMMA_NODES: Record<string, EmmaNodeDefinition> = {
  'text-input': textInputNode,
  'image-input': imageInputNode,
  'ideogram-generator': ideogramGeneratorNode,
  'dalle-generator': dalleGeneratorNode,
  'upscale': upscaleNode,
  'background-remover': backgroundRemoverNode,
  'style-transfer': styleTransferNode,
  'video-generator': videoGeneratorNode,
  'pika-video-generator': pikaVideoNode,
  'runway-video-generator': runwayVideoNode,
  'stable-video-diffusion': stableVideoNode,
  'animate-diff-video': animateDiffVideoNode,
  'video-upscale': videoUpscaleNode,
  'video-trim': videoTrimNode,
  'video-merge': videoMergeNode,
  'video-text-overlay': videoTextOverlayNode,
  'video-audio-overlay': videoAudioNode,
  'video-effects': videoEffectsNode,
  'video-speed-control': videoSpeedNode,
  'image-output': imageOutputNode,
  'video-output': videoOutputNode
};

// Categorías de nodos
export const NODE_CATEGORIES = {
  'Entrada': ['text-input', 'image-input'],
  'Generación': ['ideogram-generator', 'dalle-generator'],
  'Edición': ['upscale', 'background-remover', 'style-transfer'],
  'Video AI': ['video-generator', 'pika-video-generator', 'runway-video-generator', 'stable-video-diffusion', 'animate-diff-video'],
  'Video Edición': ['video-upscale', 'video-trim', 'video-merge', 'video-text-overlay', 'video-audio-overlay', 'video-effects', 'video-speed-control'],
  'Salida': ['image-output', 'video-output']
};
