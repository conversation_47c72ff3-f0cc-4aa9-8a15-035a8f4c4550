/**
 * Plantillas de workflows predefinidas para Emma Studio
 * Workflows listos para usar que los usuarios pueden cargar y personalizar
 */

import { EmmaWorkflow } from '@/types/workflow-types';

export const WORKFLOW_TEMPLATES: Record<string, EmmaWorkflow> = {
  'basic-image-generation': {
    id: 'basic-image-generation',
    name: 'Generación Básica de Imagen',
    description: 'Crea una imagen desde texto y mejórala automáticamente',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'beautiful landscape, mountains, sunset, masterpiece'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 400, y: 100 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            aspect_ratio: '16:9',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'upscale-1',
        type: 'emmaNode',
        position: { x: 700, y: 100 },
        data: {
          nodeType: 'upscale',
          definition: {} as any,
          inputs: {
            scale_factor: 2,
            creativity: 0.3
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1000, y: 100 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'landscape-hd',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'ideogram-1',
        target: 'upscale-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e3-4',
        source: 'upscale-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['imagen', 'básico', 'ideogram'],
      category: 'Generación de Imágenes'
    }
  },

  'dalle-background-removal': {
    id: 'dalle-background-removal',
    name: 'DALL-E + Fondo Transparente',
    description: 'Genera imagen con DALL-E y remueve el fondo automáticamente',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'cute robot character, white background, product shot'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'dalle-1',
        type: 'emmaNode',
        position: { x: 400, y: 100 },
        data: {
          nodeType: 'dalle-generator',
          definition: {} as any,
          inputs: {
            model: 'dall-e-3',
            size: '1024x1024',
            quality: 'hd'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'bg-remove-1',
        type: 'emmaNode',
        position: { x: 700, y: 100 },
        data: {
          nodeType: 'background-remover',
          definition: {} as any,
          inputs: {},
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1000, y: 100 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'robot-transparent',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'dalle-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'dalle-1',
        target: 'bg-remove-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e3-4',
        source: 'bg-remove-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['dalle', 'fondo', 'transparente'],
      category: 'Edición de Imágenes'
    }
  },

  'style-transfer-workflow': {
    id: 'style-transfer-workflow',
    name: 'Transferencia de Estilo',
    description: 'Combina dos estilos diferentes en una imagen final',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 50 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'modern city skyline, architecture'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'text-input-2',
        type: 'emmaNode',
        position: { x: 100, y: 200 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'van gogh painting style, impressionist'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 350, y: 50 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'dalle-1',
        type: 'emmaNode',
        position: { x: 350, y: 200 },
        data: {
          nodeType: 'dalle-generator',
          definition: {} as any,
          inputs: {
            model: 'dall-e-3',
            size: '1024x1024'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'style-transfer-1',
        type: 'emmaNode',
        position: { x: 600, y: 125 },
        data: {
          nodeType: 'style-transfer',
          definition: {} as any,
          inputs: {
            strength: 0.7
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 850, y: 125 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'city-vangogh-style',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-3',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-4',
        source: 'text-input-2',
        target: 'dalle-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e3-5',
        source: 'ideogram-1',
        target: 'style-transfer-1',
        sourceHandle: 'image',
        targetHandle: 'content_image'
      },
      {
        id: 'e4-5',
        source: 'dalle-1',
        target: 'style-transfer-1',
        sourceHandle: 'image',
        targetHandle: 'style_image'
      },
      {
        id: 'e5-6',
        source: 'style-transfer-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['estilo', 'artístico', 'combinación'],
      category: 'Arte y Estilo'
    }
  },

  'video-creation-workflow': {
    id: 'video-creation-workflow',
    name: 'Creación de Video',
    description: 'Genera imagen y convierte a video con movimiento',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'serene lake with mountains, golden hour'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 350, y: 100 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            aspect_ratio: '16:9',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-1',
        type: 'emmaNode',
        position: { x: 600, y: 100 },
        data: {
          nodeType: 'video-generator',
          definition: {} as any,
          inputs: {
            prompt: 'camera slowly panning across the lake, gentle water movement',
            duration: '5',
            aspect_ratio: '16:9'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 850, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'lake-cinematic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'ideogram-1',
        target: 'video-1',
        sourceHandle: 'image',
        targetHandle: 'reference_image'
      },
      {
        id: 'e3-4',
        source: 'video-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['video', 'luma', 'cinematico'],
      category: 'Generación de Video'
    }
  },

  'professional-video-editing': {
    id: 'professional-video-editing',
    name: 'Edición Profesional de Video',
    description: 'Workflow completo: genera video, agrega texto, efectos y audio',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'futuristic city at night, neon lights, cyberpunk style'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'runway-video-1',
        type: 'emmaNode',
        position: { x: 300, y: 100 },
        data: {
          nodeType: 'runway-video-generator',
          definition: {} as any,
          inputs: {
            duration: '10',
            camera_motion: 'pan_right'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-text-1',
        type: 'emmaNode',
        position: { x: 550, y: 50 },
        data: {
          nodeType: 'video-text-overlay',
          definition: {} as any,
          inputs: {
            text: 'CIUDAD DEL FUTURO',
            font_size: 72,
            position: 'top',
            color: 'white',
            duration: 3
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-effects-1',
        type: 'emmaNode',
        position: { x: 800, y: 100 },
        data: {
          nodeType: 'video-effects',
          definition: {} as any,
          inputs: {
            effect_type: 'neon',
            intensity: 0.8,
            saturation: 1.5
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-audio-1',
        type: 'emmaNode',
        position: { x: 1050, y: 100 },
        data: {
          nodeType: 'video-audio-overlay',
          definition: {} as any,
          inputs: {
            audio_type: 'background_music',
            volume: 30,
            fade_in_audio: 2,
            loop_audio: 'yes'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1300, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'cyberpunk-city-final'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'runway-video-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'runway-video-1',
        target: 'video-text-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e3-4',
        source: 'video-text-1',
        target: 'video-effects-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e4-5',
        source: 'video-effects-1',
        target: 'video-audio-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e5-6',
        source: 'video-audio-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['video', 'profesional', 'efectos', 'audio'],
      category: 'Video Profesional'
    }
  },

  'multi-platform-video': {
    id: 'multi-platform-video',
    name: 'Video Multi-Plataforma',
    description: 'Crea videos optimizados para diferentes redes sociales',
    nodes: [
      {
        id: 'image-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 150 },
        data: {
          nodeType: 'image-input',
          definition: {} as any,
          inputs: {},
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'stable-video-1',
        type: 'emmaNode',
        position: { x: 300, y: 50 },
        data: {
          nodeType: 'stable-video-diffusion',
          definition: {} as any,
          inputs: {
            motion_bucket_id: 180,
            cond_aug: 0.02
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'pika-video-1',
        type: 'emmaNode',
        position: { x: 300, y: 200 },
        data: {
          nodeType: 'pika-video-generator',
          definition: {} as any,
          inputs: {
            style: 'realistic',
            motion_strength: 0.8
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-trim-1',
        type: 'emmaNode',
        position: { x: 550, y: 50 },
        data: {
          nodeType: 'video-trim',
          definition: {} as any,
          inputs: {
            start_time: 0,
            end_time: 15,
            fade_in: 0.5,
            fade_out: 0.5
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-trim-2',
        type: 'emmaNode',
        position: { x: 550, y: 200 },
        data: {
          nodeType: 'video-trim',
          definition: {} as any,
          inputs: {
            start_time: 0,
            end_time: 30,
            fade_in: 1,
            fade_out: 1
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-upscale-1',
        type: 'emmaNode',
        position: { x: 800, y: 125 },
        data: {
          nodeType: 'video-upscale',
          definition: {} as any,
          inputs: {
            scale_factor: '2x',
            model: 'artemis-hq',
            fps_conversion: '30fps'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'image-input-1',
        target: 'stable-video-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e1-3',
        source: 'image-input-1',
        target: 'pika-video-1',
        sourceHandle: 'image',
        targetHandle: 'reference_image'
      },
      {
        id: 'e2-4',
        source: 'stable-video-1',
        target: 'video-trim-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e3-5',
        source: 'pika-video-1',
        target: 'video-trim-2',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e4-6',
        source: 'video-trim-1',
        target: 'video-upscale-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['multi-plataforma', 'redes-sociales', 'optimizado'],
      category: 'Video Profesional'
    }
  },

  'advanced-video-effects': {
    id: 'advanced-video-effects',
    name: 'Efectos Avanzados de Video',
    description: 'Workflow con múltiples efectos: velocidad, merge y efectos visuales',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'dancer in motion, studio lighting, dynamic movement'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'animate-diff-1',
        type: 'emmaNode',
        position: { x: 300, y: 100 },
        data: {
          nodeType: 'animate-diff-video',
          definition: {} as any,
          inputs: {
            frames: 24,
            guidance_scale: 8.0,
            negative_prompt: 'blurry, low quality'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-speed-1',
        type: 'emmaNode',
        position: { x: 550, y: 50 },
        data: {
          nodeType: 'video-speed-control',
          definition: {} as any,
          inputs: {
            speed_factor: 0.5,
            effect_type: 'slow_motion',
            preserve_audio: 'pitch_correct'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-speed-2',
        type: 'emmaNode',
        position: { x: 550, y: 150 },
        data: {
          nodeType: 'video-speed-control',
          definition: {} as any,
          inputs: {
            speed_factor: 2.0,
            effect_type: 'time_lapse',
            preserve_audio: 'no'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-merge-1',
        type: 'emmaNode',
        position: { x: 800, y: 100 },
        data: {
          nodeType: 'video-merge',
          definition: {} as any,
          inputs: {
            transition: 'fade',
            transition_duration: 1.0
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-effects-1',
        type: 'emmaNode',
        position: { x: 1050, y: 100 },
        data: {
          nodeType: 'video-effects',
          definition: {} as any,
          inputs: {
            effect_type: 'glitch',
            intensity: 0.3,
            saturation: 1.2
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1300, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'dancer-effects-final'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'animate-diff-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'animate-diff-1',
        target: 'video-speed-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e2-4',
        source: 'animate-diff-1',
        target: 'video-speed-2',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e3-5',
        source: 'video-speed-1',
        target: 'video-merge-1',
        sourceHandle: 'video',
        targetHandle: 'video1'
      },
      {
        id: 'e4-5',
        source: 'video-speed-2',
        target: 'video-merge-1',
        sourceHandle: 'video',
        targetHandle: 'video2'
      },
      {
        id: 'e5-6',
        source: 'video-merge-1',
        target: 'video-effects-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e6-7',
        source: 'video-effects-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['efectos', 'velocidad', 'merge', 'avanzado'],
      category: 'Video Profesional'
    }
  }
};

export const TEMPLATE_CATEGORIES = {
  'Generación de Imágenes': ['basic-image-generation'],
  'Edición de Imágenes': ['dalle-background-removal'],
  'Arte y Estilo': ['style-transfer-workflow'],
  'Generación de Video': ['video-creation-workflow'],
  'Video Profesional': ['professional-video-editing', 'multi-platform-video', 'advanced-video-effects']
};
