/**
 * Emma Visual Workflow Builder - Sistema simplificado de workflows visuales
 * Drag & Drop con React Flow para crear campañas de contenido AI
 *
 * El usuario solo selecciona qué herramientas usar, nosotros manejamos las APIs
 * Sistema 100% Emma Studio - Sin ComfyUI, sin configuraciones complejas
 */

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';

import {
  Play,
  Save,
  Download,
  Zap,
  Plus,
  Settings,
  X
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { EmmaWorkflow, EmmaWorkflowNode, EmmaWorkflowEdge } from '@/types/workflow-types';
import { EMMA_NODES, NODE_CATEGORIES } from '@/data/emma-nodes';
import { WORKFLOW_TEMPLATES } from '@/data/workflow-templates';
import EmmaNode from '@/components/workflow-builder/EmmaNode';

// Tipos de nodos personalizados
const nodeTypes = {
  emmaNode: EmmaNode,
};

// Nodos iniciales de ejemplo
const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

const AIWorkflowBuilderPage: React.FC = () => {
  // Estados del workflow
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [showNodePanel, setShowNodePanel] = useState(true);
  const [showTemplates, setShowTemplates] = useState(false);
  const [executionResults, setExecutionResults] = useState<any>(null);
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [nodeStates, setNodeStates] = useState<Record<string, any>>({});

  // Handlers del workflow
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id);
  }, []);

  const addNode = useCallback((nodeType: string) => {
    const nodeDefinition = EMMA_NODES[nodeType];
    if (!nodeDefinition) return;

    const newNode: Node = {
      id: `${nodeType}-${Date.now()}`,
      type: 'emmaNode',
      position: {
        x: Math.random() * 400 + 100,
        y: Math.random() * 400 + 100
      },
      data: {
        nodeType,
        definition: nodeDefinition,
        inputs: {},
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      },
    };

    setNodes((nds) => nds.concat(newNode));
  }, [setNodes]);

  const updateNodeState = useCallback((nodeId: string, state: any) => {
    setNodeStates(prev => ({
      ...prev,
      [nodeId]: state
    }));

    // Actualizar el nodo en React Flow
    setNodes(nds => nds.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            ...state
          }
        };
      }
      return node;
    }));
  }, [setNodes]);

  const validateWorkflow = useCallback(() => {
    const errors: string[] = [];

    if (nodes.length === 0) {
      errors.push('El workflow debe tener al menos un nodo');
    }

    // Validar que los nodos de generación tengan prompts
    nodes.forEach(node => {
      if ((node.data.nodeType === 'ideogram-generator' || node.data.nodeType === 'dalle-generator')) {
        const hasPrompt = node.data.inputs?.prompt ||
          edges.some(edge => edge.target === node.id && edge.targetHandle === 'prompt');

        if (!hasPrompt) {
          errors.push(`${EMMA_NODES[node.data.nodeType]?.name}: Se requiere un prompt`);
        }
      }
    });

    return errors;
  }, [nodes, edges]);

  const executeWorkflow = useCallback(async () => {
    // Validar antes de ejecutar
    const validationErrors = validateWorkflow();
    if (validationErrors.length > 0) {
      setExecutionError(`Errores de validación:\n${validationErrors.join('\n')}`);
      return;
    }

    setIsExecuting(true);
    setExecutionError(null);
    setExecutionResults(null);

    try {
      // Limpiar estados previos
      setNodeStates({});

      // Marcar todos los nodos como no ejecutados
      setNodes(nds => nds.map(node => ({
        ...node,
        data: {
          ...node.data,
          isExecuting: false,
          isCompleted: false,
          errors: []
        }
      })));

      // Convertir workflow a formato de ejecución
      const workflow = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.data.nodeType || 'unknown',
          inputs: node.data.inputs || {},
          position: node.position
        })),
        edges: edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }))
      };

      console.log('🚀 Ejecutando workflow:', workflow);

      // Ejecutar workflow
      const response = await fetch('http://localhost:8000/api/v1/emma-workflows/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflow })
      });

      const result = await response.json();
      console.log('✅ Resultado:', result);

      if (result.success) {
        setExecutionResults(result.results);

        // Actualizar nodos con resultados
        Object.entries(result.results).forEach(([nodeId, nodeResult]: [string, any]) => {
          updateNodeState(nodeId, {
            isCompleted: nodeResult.success,
            isExecuting: false,
            errors: nodeResult.success ? [] : [nodeResult.message],
            outputs: nodeResult.outputs || {}
          });
        });

        console.log('🎉 Workflow ejecutado exitosamente');
      } else {
        setExecutionError(result.message || 'Error desconocido');
        console.error('❌ Error en workflow:', result);
      }

    } catch (error) {
      console.error('❌ Error ejecutando workflow:', error);
      setExecutionError(error instanceof Error ? error.message : 'Error de conexión');
    } finally {
      setIsExecuting(false);
    }
  }, [nodes, edges, setNodes, updateNodeState]);

  const saveWorkflow = useCallback(async () => {
    if (nodes.length === 0) {
      alert('No hay nodos para guardar');
      return;
    }

    const workflowName = prompt('Nombre del workflow:', 'Mi Workflow Emma') || 'Mi Workflow Emma';
    const workflowDescription = prompt('Descripción (opcional):', 'Workflow creado con Emma Studio') || '';

    try {
      const workflow = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.data.nodeType || 'unknown',
          inputs: node.data.inputs || {},
          position: node.position
        })),
        edges: edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }))
      };

      const response = await fetch('http://localhost:8000/api/v1/emma-workflows/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: workflowName,
          description: workflowDescription,
          workflow,
          tags: ['emma-studio', 'visual-workflow'],
          category: 'user-created'
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ Workflow guardado: ${workflowName}`);
        console.log('Workflow guardado:', result);
      } else {
        alert('❌ Error guardando workflow');
      }
    } catch (error) {
      console.error('Error guardando workflow:', error);
      alert('❌ Error de conexión al guardar');
    }
  }, [nodes, edges]);

  const loadTemplate = useCallback((templateId: string) => {
    const template = WORKFLOW_TEMPLATES[templateId];
    if (!template) return;

    setNodes(template.nodes);
    setEdges(template.edges);
    setShowTemplates(false);
  }, [setNodes, setEdges]);

  // WORKFLOW ESTILO WEAVY - Horizontal como en la imagen
  const createWeavyWorkflow = useCallback(() => {
    const promptNode: Node = {
      id: 'prompt-weavy-1',
      type: 'emmaNode',
      position: { x: 50, y: 200 },
      data: {
        nodeType: 'text-input',
        definition: EMMA_NODES['text-input'],
        inputs: { text: 'Una mujer joven con cabello rizado trabajando en una computadora, estilo profesional, iluminación natural' },
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      }
    };

    const ideogramNode: Node = {
      id: 'ideogram-weavy-1',
      type: 'emmaNode',
      position: { x: 450, y: 150 },
      data: {
        nodeType: 'ideogram-generator',
        definition: EMMA_NODES['ideogram-generator'],
        inputs: {
          model: 'ideogram-3.0',
          aspect_ratio: '16:9',
          style: 'realistic'
        },
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      }
    };

    const dalleNode: Node = {
      id: 'dalle-weavy-1',
      type: 'emmaNode',
      position: { x: 450, y: 300 },
      data: {
        nodeType: 'dalle-generator',
        definition: EMMA_NODES['dalle-generator'],
        inputs: {
          model: 'dall-e-3',
          size: '1024x1024',
          quality: 'hd'
        },
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      }
    };

    const upscaleNode: Node = {
      id: 'upscale-weavy-1',
      type: 'emmaNode',
      position: { x: 850, y: 200 },
      data: {
        nodeType: 'image-upscale',
        definition: EMMA_NODES['image-upscale'],
        inputs: {
          scale: '2x',
          model: 'real-esrgan'
        },
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      }
    };

    const weavyEdges: Edge[] = [
      {
        id: 'edge-prompt-ideogram',
        source: 'prompt-weavy-1',
        target: 'ideogram-weavy-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'edge-prompt-dalle',
        source: 'prompt-weavy-1',
        target: 'dalle-weavy-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'edge-ideogram-upscale',
        source: 'ideogram-weavy-1',
        target: 'upscale-weavy-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ];

    setNodes([promptNode, ideogramNode, dalleNode, upscaleNode]);
    setEdges(weavyEdges);
  }, [setNodes, setEdges]);

  // Componente de panel de nodos
  const NodePanel = () => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="w-80 bg-black/20 backdrop-blur-sm border-r border-purple-500/20 p-4"
    >
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Plus className="h-5 w-5 mr-2 text-purple-400" />
        Agregar Nodos
      </h3>

      {Object.entries(NODE_CATEGORIES).map(([category, nodeIds]) => (
        <div key={category} className="mb-6">
          <h4 className="text-sm font-medium text-purple-200 mb-3">{category}</h4>
          <div className="space-y-2">
            {nodeIds.map(nodeId => {
              const node = EMMA_NODES[nodeId];
              return (
                <button
                  key={nodeId}
                  onClick={() => addNode(nodeId)}
                  className="w-full p-3 bg-purple-500/10 hover:bg-purple-500/20 border border-purple-500/30 rounded-lg text-left transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{node.icon}</span>
                    <div>
                      <div className="text-sm font-medium text-white">{node.name}</div>
                      <div className="text-xs text-purple-200">{node.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      ))}
    </motion.div>
  );

  // Panel de resultados
  const ResultsPanel = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="w-80 bg-black/20 backdrop-blur-sm border-l border-purple-500/20 p-4"
    >
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Zap className="h-5 w-5 mr-2 text-green-400" />
        Resultados
      </h3>

      {executionError && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
          <div className="text-red-300 text-sm font-medium mb-1">Error de Ejecución</div>
          <div className="text-red-200 text-xs">{executionError}</div>
        </div>
      )}

      {executionResults && (
        <div className="space-y-4">
          {Object.entries(executionResults).map(([nodeId, result]: [string, any]) => {
            const node = nodes.find(n => n.id === nodeId);
            if (!node || !result.success) return null;

            return (
              <div key={nodeId} className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-3">
                <div className="text-sm font-medium text-white mb-2 flex items-center">
                  <span className="text-lg mr-2">{EMMA_NODES[node.data.nodeType]?.icon}</span>
                  {EMMA_NODES[node.data.nodeType]?.name}
                </div>

                {result.outputs?.image && (
                  <div className="mb-2">
                    <img
                      src={result.outputs.image}
                      alt="Generated"
                      className="w-full rounded-lg border border-green-500/30"
                    />
                  </div>
                )}

                {result.outputs?.video && (
                  <div className="mb-2">
                    <video
                      src={result.outputs.video}
                      controls
                      className="w-full rounded-lg border border-green-500/30"
                    />
                  </div>
                )}

                {result.outputs?.text && (
                  <div className="mb-2 p-2 bg-black/20 rounded text-xs text-purple-200">
                    {result.outputs.text}
                  </div>
                )}

                <div className="text-xs text-green-300">
                  ✅ {result.message}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {!executionResults && !executionError && (
        <div className="text-center text-purple-300 text-sm">
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
            <Zap className="h-8 w-8 text-purple-400" />
          </div>
          <p>Ejecuta un workflow para ver los resultados aquí</p>
        </div>
      )}
    </motion.div>
  );

  return (
    <ReactFlowProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-8 w-8 text-purple-400" />
                <h1 className="text-2xl font-bold text-white">Emma Visual Workflows</h1>
              </div>
              <Badge variant="outline" className="border-purple-400 text-purple-300">
                Sistema Emma
              </Badge>
              <Badge variant="outline" className="border-green-400 text-green-300">
                {nodes.length} Nodos
              </Badge>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowTemplates(true)}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
              >
                <Download className="h-4 w-4 mr-2" />
                Plantillas
              </Button>

              <Button
                variant="outline"
                onClick={saveWorkflow}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
              >
                <Save className="h-4 w-4 mr-2" />
                Guardar
              </Button>

              <Button
                onClick={createWeavyWorkflow}
                variant="outline"
                className="border-green-500/30 text-green-200 hover:bg-green-500/20"
              >
                <Zap className="h-4 w-4 mr-2" />
                Workflow Weavy
              </Button>

              <Button
                onClick={executeWorkflow}
                disabled={isExecuting || nodes.length === 0}
                className={`${
                  validateWorkflow().length > 0
                    ? 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800'
                    : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
                }`}
                title={validateWorkflow().length > 0 ? validateWorkflow().join('\n') : ''}
              >
                {isExecuting ? (
                  <>
                    <Settings className="h-4 w-4 mr-2 animate-spin" />
                    Ejecutando...
                  </>
                ) : validateWorkflow().length > 0 ? (
                  <>
                    <X className="h-4 w-4 mr-2" />
                    Errores ({validateWorkflow().length})
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Ejecutar
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowNodePanel(!showNodePanel)}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Main Content */}
        <div className="flex h-[calc(100vh-120px)]">
          {/* Node Panel */}
          {showNodePanel && <NodePanel />}

          {/* Workflow Canvas */}
          <div className="flex-1 relative">
            {/* Panel de resultados flotante */}
            {(executionResults || executionError) && (
              <div className="absolute top-4 right-4 z-10">
                <ResultsPanel />
              </div>
            )}
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              fitView
              className="bg-transparent"
            >
              <Background
                variant={BackgroundVariant.Dots}
                gap={20}
                size={1}
                color="rgba(139, 92, 246, 0.2)"
              />
              <Controls
                className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
              />
              <MiniMap
                className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
                nodeColor="#8b5cf6"
                maskColor="rgba(0, 0, 0, 0.2)"
              />
            </ReactFlow>

            {/* Empty State */}
            {nodes.length === 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute inset-0 flex items-center justify-center pointer-events-none"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Zap className="h-8 w-8 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Crea tu primer workflow Emma
                  </h3>
                  <p className="text-purple-200 mb-4 max-w-md">
                    Arrastra nodos desde el panel lateral para crear flujos de trabajo visuales.
                    Solo selecciona las herramientas, nosotros manejamos todo lo técnico.
                  </p>
                  <div className="space-y-2 text-sm text-purple-300">
                    <p>🎨 Usa nuestras APIs de Ideogram y DALL-E</p>
                    <p>✨ Edita automáticamente con Stability AI</p>
                    <p>🎬 Genera videos con Luma Labs integrado</p>
                    <p>🚀 Sin configuración, sin APIs, solo resultados</p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>

        {/* Modal de Plantillas */}
        {showTemplates && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={() => setShowTemplates(false)}
          >
            <Card
              className="bg-black/80 backdrop-blur-sm border-purple-500/30 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white flex items-center justify-between">
                  <span>🎨 Plantillas de Workflows Emma</span>
                  <Button
                    variant="ghost"
                    onClick={() => setShowTemplates(false)}
                    className="text-purple-300 hover:bg-purple-500/20"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.values(WORKFLOW_TEMPLATES).map((template) => (
                  <motion.div
                    key={template.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg cursor-pointer hover:bg-purple-500/20 transition-colors"
                    onClick={() => loadTemplate(template.id)}
                  >
                    <h3 className="text-lg font-semibold text-white mb-2">{template.name}</h3>
                    <p className="text-sm text-purple-200 mb-3">{template.description}</p>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {template.metadata.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="border-purple-400 text-purple-300 text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center justify-between text-xs text-purple-300">
                      <span>{template.nodes.length} nodos</span>
                      <span>{template.metadata.category}</span>
                    </div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </ReactFlowProvider>
  );
};

export default AIWorkflowBuilderPage;
